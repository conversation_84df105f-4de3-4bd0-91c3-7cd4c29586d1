#!/usr/bin/env python3
"""
Test script to verify that model training works with datasets containing only 'ds' and 'y' columns
"""

import sys
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# Add the current directory to the path
sys.path.append('.')

# Import the training functions
from sales_forecast.pipeline.model_training import model_training

def create_test_data():
    """Create test data with only ds and y columns"""
    # Create monthly data for 3 years to have enough data for seasonal models
    dates = pd.date_range(start='2021-01-01', end='2023-12-31', freq='ME')  # Use 'ME' instead of deprecated 'M'

    # Create synthetic sales data with trend and seasonality
    np.random.seed(42)
    trend = np.linspace(1000, 2000, len(dates))
    seasonal = 300 * np.sin(2 * np.pi * np.arange(len(dates)) / 12)
    noise = np.random.normal(0, 100, len(dates))
    sales = trend + seasonal + noise

    data = pd.DataFrame({
        'ds': dates,
        'y': sales
    })

    return data

def test_training_no_features():
    """Test model training with ds and y only"""
    print("=== Testing Model Training with DS and Y Only ===")

    # Create test data
    data = create_test_data()
    print(f"Created test data with {len(data)} records")
    print("Data columns:", data.columns.tolist())
    print("\nFirst few rows:")
    print(data.head())

    # Split data for training and testing
    split_point = int(len(data) * 0.8)
    train_data = data[:split_point].copy()
    test_data = data[split_point:].copy()

    print(f"\nTrain data: {len(train_data)} records")
    print(f"Test data: {len(test_data)} records")

    # Test training with no features
    feature_columns = []  # Empty feature list
    date_column = 'ds'
    target_column = 'y'

    print(f"\nTraining with:")
    print(f"- Date column: {date_column}")
    print(f"- Target column: {target_column}")
    print(f"- Feature columns: {feature_columns} (empty)")

    try:
        result = model_training(
            train_data=train_data,
            test_data=test_data,
            data=data,
            date_column=date_column,
            target_column=target_column,
            feature_columns=feature_columns
        )

        best_model, best_accuracy, best_prediction, xgb_accuracy, es_accuracy, sarima_accuracy, prophet_accuracy, best_model_name = result

        print("\n✅ SUCCESS: Model training completed!")
        print(f"Best model: {best_model_name}")
        print(f"Best accuracy: {best_accuracy:.2f}%")
        print(f"Exponential Smoothing accuracy: {es_accuracy:.2f}%")
        print(f"SARIMA accuracy: {sarima_accuracy:.2f}%")
        print(f"XGBoost accuracy: {xgb_accuracy:.2f}%")
        print(f"Prophet accuracy: {prophet_accuracy:.2f}%")

        return True

    except Exception as e:
        print(f"❌ ERROR: Model training failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_training_no_features()
    if success:
        print("\n🎉 All tests passed! Model training works with ds-only datasets.")
    else:
        print("\n💥 Tests failed. Please check the error messages above.")
