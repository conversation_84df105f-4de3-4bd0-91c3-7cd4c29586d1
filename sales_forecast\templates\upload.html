{% extends "base.html" %}

{% block title %}Sales Forecast - Upload Dataset{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h2 class="mb-0">Upload Dataset</h2>
            </div>
            <div class="card-body">
                <form method="POST" enctype="multipart/form-data" class="needs-validation" novalidate>
                    <div class="mb-3">
                        <label for="file" class="form-label">Select CSV File</label>
                        <div class="upload-container">
                            <input type="file" class="form-control" id="file" name="file" accept=".csv" required>
                            <div class="invalid-feedback">Please select a CSV file.</div>
                            <p id="file-name" class="mt-2 text-muted">No file selected</p>
                        </div>
                        <div class="form-text">Upload a CSV file containing your sales data.</div>
                    </div>

                    <div class="alert alert-info">
                        <h5>Dataset Requirements</h5>
                        <ul>
                            <li>File must be in CSV format</li>
                            <li>Dataset must have at least 10 rows</li>
                            <li>Should contain a date column</li>
                            <li>Dataset should contain monthly data</li>
                            <li>Should contain numeric feature columns</li>
                            <li>Should contain a target column (sales)</li>
                        </ul>
                    </div>

                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-upload"></i> Upload and Train Model
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
