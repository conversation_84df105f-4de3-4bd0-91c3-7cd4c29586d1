from sales_forecast.pipeline.data_preparation import clean_data, data_identifying
from sklearn.model_selection import TimeSeriesSplit, GridSearchCV
from sklearn.metrics import mean_squared_error
from statsmodels.tsa.holtwinters import ExponentialSmoothing
from statsmodels.tsa.statespace.sarimax import SARIMAX
from xgboost import XGBRegressor
from prophet import Prophet
import itertools
import numpy as np
import pandas as pd


# data, date_column, target_column, feature_columns = data_identifying(data,y)

# def split_data(data):
#   #assuming prediction is for 2 data points
#   train_data = data[[date_column]+feature_columns+[target_column]][:-3]
#   test_data = data[[date_column]+feature_columns+[target_column]][-3:]
#   return train_data,test_data

def ensemble_predict(predictions):
    return np.mean(predictions, axis=0)

def model_training(train_data,test_data,data,date_column,target_column,feature_columns):
  # Exponential Smoothing with Hyperparameter Tuning
  best_mse = float("inf")
  best_model = None

  # Check if we have enough data for seasonal models
  min_seasonal_data = 24  # Need at least 2 years of monthly data for seasonal=12

  if len(train_data) >= min_seasonal_data:
      # Try seasonal models
      for trend in ['add', 'mul']:
          for seasonal in ['add', 'mul']:
              for seasonal_periods in [6, 12]:
                  try:
                      model = ExponentialSmoothing(train_data[target_column], trend=trend, seasonal=seasonal, seasonal_periods=seasonal_periods).fit()
                      pred = model.forecast(len(test_data))
                      mse = mean_squared_error(test_data[target_column], pred)
                      if mse < best_mse:
                          best_mse = mse
                          best_model = model
                  except Exception as e:
                      print(f"Skipping seasonal model {trend}/{seasonal}/{seasonal_periods}: {e}")
                      continue

  # If no seasonal model worked, try simple models
  if best_model is None:
      print("Trying non-seasonal exponential smoothing models...")
      for trend in ['add', 'mul', None]:
          try:
              model = ExponentialSmoothing(train_data[target_column], trend=trend, seasonal=None).fit()
              pred = model.forecast(len(test_data))
              mse = mean_squared_error(test_data[target_column], pred)
              if mse < best_mse:
                  best_mse = mse
                  best_model = model
          except Exception as e:
              print(f"Skipping trend model {trend}: {e}")
              continue

  es_model = best_model
  if es_model is not None:
      es_pred = es_model.forecast(len(test_data))
  else:
      print("Warning: Could not fit Exponential Smoothing model")
      es_pred = np.array([train_data[target_column].mean()] * len(test_data))
  # SARIMA with Hyperparameter Tuning
  try:
      if len(train_data) >= min_seasonal_data:
          # Try seasonal SARIMA
          sarima_model = SARIMAX(train_data[target_column], order=(2, 1, 2), seasonal_order=(1, 0, 1, 12))
      else:
          # Use simple ARIMA for small datasets
          print("Using simple ARIMA instead of SARIMA for small dataset")
          sarima_model = SARIMAX(train_data[target_column], order=(1, 1, 1))

      sarima_results = sarima_model.fit(disp=False)
      sarima_pred = sarima_results.forecast(len(test_data))
  except Exception as e:
      print(f"Warning: Could not fit SARIMA model: {e}")
      sarima_results = None
      sarima_pred = np.array([train_data[target_column].mean()] * len(test_data))

  # XGBoost with Hyperparameter Tuning (only if features available)
  has_features = feature_columns and len(feature_columns) > 0

  if has_features:
      print("Training XGBoost with features...")
      param_grid = {
          'n_estimators': [100, 200, 300],
          'learning_rate': [0.1, 0.2, 0.3],
      }
      xgb_model = XGBRegressor()
      grid_search = GridSearchCV(xgb_model, param_grid, cv=TimeSeriesSplit(n_splits=3), scoring='neg_mean_squared_error')
      grid_search.fit(train_data[feature_columns], train_data[[target_column]])
      xgb_model = grid_search.best_estimator_
      xgb_pred = xgb_model.predict(test_data[feature_columns])
  else:
      print("Skipping XGBoost training - no features available")
      xgb_model = None
      xgb_pred = np.array([0] * len(test_data))  # Dummy predictions

  # Prophet model
  # Prepare the data for Prophet
  prophet_data = train_data[[date_column, target_column]].rename(columns={date_column: 'ds', target_column: 'y'})

  prophet_model = Prophet()

  if has_features:
      print("Training Prophet with regressors...")
      for regressor in feature_columns:
          prophet_model.add_regressor(regressor)
      # Add feature columns to prophet_data
      for col in feature_columns:
          prophet_data[col] = train_data[col]
      prophet_model.fit(prophet_data)

      # Predict future dates with features
      future = test_data[[date_column]].rename(columns={date_column: 'ds'})
      for regressor in feature_columns:
          future[regressor] = test_data[regressor]
  else:
      print("Training Prophet without regressors...")
      prophet_model.fit(prophet_data)

      # Predict future dates without features
      future = test_data[[date_column]].rename(columns={date_column: 'ds'})

  forecast = prophet_model.predict(future)
  prophet_pred = forecast['yhat'].values

  # Calculate RMSE for each model
  es_rmse = np.sqrt(mean_squared_error(test_data[target_column], es_pred))
  sarima_rmse = np.sqrt(mean_squared_error(test_data[target_column], sarima_pred))
  prophet_rmse = np.sqrt(mean_squared_error(test_data[target_column], prophet_pred))

  if has_features and xgb_model is not None:
      xgb_rmse = np.sqrt(mean_squared_error(test_data[target_column], xgb_pred))
      xgb_accuracy = 100 - (xgb_rmse / test_data[target_column].mean()) * 100
  else:
      xgb_rmse = float('inf')  # Very high error for skipped model
      xgb_accuracy = 0  # No accuracy for skipped model

  # Calculate accuracy for each model
  es_accuracy = 100 - (es_rmse / test_data[target_column].mean()) * 100
  sarima_accuracy = 100 - (sarima_rmse / test_data[target_column].mean()) * 100
  prophet_accuracy = 100 - (prophet_rmse / test_data[target_column].mean()) * 100

  # Print the results
  print(f'Exponential Smoothing RMSE: {es_rmse:.2f}')
  print(f'Exponential Smoothing Accuracy: {es_accuracy:.2f}%')

  print(f'SARIMA RMSE: {sarima_rmse:.2f}')
  print(f'SARIMA Accuracy: {sarima_accuracy:.2f}%')

  if has_features and xgb_model is not None:
      print(f'XGBoost RMSE: {xgb_rmse:.2f}')
      print(f'XGBoost Accuracy: {xgb_accuracy:.2f}%')
  else:
      print('XGBoost: Skipped (no features available)')

  print(f'Prophet RMSE: {prophet_rmse:.2f}')
  print(f'Prophet Accuracy: {prophet_accuracy:.2f}%')

  # Combine predictions from models with accuracy > 50%
  predictions = []
  if es_accuracy > 50:
    predictions.append(es_pred)
  if sarima_accuracy > 50:
    predictions.append(sarima_pred)
  if has_features and xgb_model is not None and xgb_accuracy > 50:
    predictions.append(xgb_pred)
  if prophet_accuracy > 50:
    predictions.append(prophet_pred)
  # Calculate ensemble prediction
  if predictions:
    ensemble_pred = ensemble_predict(np.array(predictions))
    ensemble_rmse = np.sqrt(mean_squared_error(test_data[target_column], ensemble_pred))
    ensemble_accuracy = 100 - (ensemble_rmse / test_data[target_column].mean()) * 100
    print(f'Ensemble RMSE: {ensemble_rmse:.2f}')
    print(f'Ensemble Accuracy: {ensemble_accuracy:.2f}%')
  else:
    ensemble_accuracy = 0
    ensemble_pred = None
    print("No models with accuracy greater than 50% to create an ensemble.")

  # Find the best model among available models
  model_accuracies = [
      ('Exponential Smoothing', es_accuracy, es_model, es_pred),
      ('Prophet', prophet_accuracy, prophet_model, prophet_pred)
  ]

  # Add SARIMA only if it was successfully trained
  if sarima_results is not None:
      model_accuracies.append(('SARIMA', sarima_accuracy, sarima_results, sarima_pred))

  # Add XGBoost only if it was trained
  if has_features and xgb_model is not None:
      model_accuracies.append(('XGBoost', xgb_accuracy, xgb_model, xgb_pred))

  # Add ensemble if available
  if ensemble_pred is not None:
      model_accuracies.append(('ensemble', ensemble_accuracy, ensemble_pred, ensemble_pred))

  # Find the best model
  best_model_info = max(model_accuracies, key=lambda x: x[1])
  best_model_name, best_accuracy, best_model, best_prediction = best_model_info

  print(f'{best_model_name} has the highest accuracy: {best_accuracy:.2f}%')
  print(best_prediction)

  if best_accuracy < 50:
      print('Warning: The best accuracy is lower than 50%. Please provide more data or check data quality.')
  # Plot the results
  # plt.figure(figsize=(10, 6))
  # plt.plot(test_data[date_column], test_data[target_column], label='Actual')
  # plt.plot(test_data[date_column], ensemble_pred, label='Ensemble Prediction')
  # plt.plot(test_data[date_column], xgb_pred, label='XGBoost')
  # plt.plot(test_data[date_column], sarima_pred, label='SARIMA')
  # plt.plot(test_data[date_column], es_pred, label='Exponential Smoothing')
  # plt.plot(test_data[date_column], prophet_pred, label='Prophet')
  # plt.xlabel('Date')
  # plt.ylabel('Sales')
  # plt.title('Prediction vs Actual Sales')
  # plt.legend()
  # plt.xticks(rotation=45)
  # plt.show()

  return best_model,best_accuracy,best_prediction,xgb_accuracy,es_accuracy,sarima_accuracy,prophet_accuracy,best_model_name

def best_model_training(data,best_model_name,feature_pred,prediction_data,feature_columns,date_column,target_column,xgb_accuracy,es_accuracy,sarima_accuracy,prophet_accuracy):
      if best_model_name == 'Exponential Smoothing':
        best_mse = float("inf")
        best_model = None
        for trend in ['add', 'mul']:
            for seasonal in ['add', 'mul']:
                for seasonal_periods in [6, 12]:
                    esmodel = ExponentialSmoothing(data[target_column], trend=trend, seasonal=seasonal, seasonal_periods=seasonal_periods).fit()
                    es_pred = esmodel.forecast(3)
        return es_pred
        print (f'exponential smoothing output{es_pred}')
        joblib.dump(esmodel, bestmodel)
        print(f"Best model '{esmodel}' saved as {bestmodel}")

      elif best_model_name == 'SARIMA':
        p = d = q = range(0, 2)
        pdq = list(itertools.product(p, d, q))
        seasonal_pdq = [(x[0], x[1], x[2], 12) for x in list(itertools.product(p, d, q))]
        param_grid = {'order': pdq, 'seasonal_order': seasonal_pdq}
        sarima_model = SARIMAX(data[target_column], **{'order': (2, 1, 2), 'seasonal_order': (1, 0, 1, 12)})
        sarima_results = sarima_model.fit()
        sarima_pred = sarima_results.forecast(3)
        return sarima_pred
        print (f'sarima output{sarima_pred}')
        joblib.dump(sarima_model, bestmodel)
        print(f"Best model '{sarima_model}' saved as {bestmodel}")

      elif best_model_name == 'XGBoost':
          param_grid = {
          'n_estimators': [100, 200, 300],
          'learning_rate': [0.1, 0.2, 0.3],
            }
          xgb_model = XGBRegressor()
          grid_search = GridSearchCV(xgb_model, param_grid, cv=TimeSeriesSplit(n_splits=3), scoring='neg_mean_squared_error')
          grid_search.fit(data[feature_columns], data[[target_column]])
          xgb_model = grid_search.best_estimator_
          xgb_pred = xgb_model.predict(feature_pred)
          return xgb_pred
          print (f'xgb output{xgb_pred}')
          joblib.dump(xgb_model, bestmodel)
          print(f"Best model '{xgb_model}' saved as {bestmodel}")

      elif best_model_name == 'Prophet':
        # prophet_data = data[[date_column, target_column]+feature_columns].rename(columns={date_column: 'ds', target_column: 'y'})
        # prophet_data.append(feature_pred)
        # prophet_data = pd.concat([prophet_data, feature_pred], axis=0)
        prophet_model = Prophet()
        for regressor in feature_columns:
            prophet_model.add_regressor(regressor)
        prophet_model.fit(data)
        # Predict future dates
        future = prediction_data[['ds']].copy()#prediction for 2 months
        for regressor in feature_columns:
            future[regressor] = feature_pred[regressor]  # Align feature values
        forecast = prophet_model.predict(future)
        prophet_pred = forecast['yhat'][-3:]
        return prophet_pred
        print (f'prophet output{prophet_pred}')
        joblib.dump(prophet_model, bestmodel)
        print(f"Best model '{prophet_model}' saved as {bestmodel}")

      else:
        best_mse = float("inf")
        best_model = None
        for trend in ['add', 'mul']:
            for seasonal in ['add', 'mul']:
                for seasonal_periods in [6, 12]:
                    model = ExponentialSmoothing(data[target_column], trend=trend, seasonal=seasonal, seasonal_periods=seasonal_periods).fit()
                    es_pred = model.forecast(3)

        p = d = q = range(0, 2)
        pdq = list(itertools.product(p, d, q))
        seasonal_pdq = [(x[0], x[1], x[2], 12) for x in list(itertools.product(p, d, q))]
        param_grid = {'order': pdq, 'seasonal_order': seasonal_pdq}
        sarima_model = SARIMAX(data[target_column], **{'order': (2, 1, 2), 'seasonal_order': (1, 0, 1, 12)})
        sarima_results = sarima_model.fit()
        sarima_pred = sarima_results.forecast(3)

        param_grid = {
            'n_estimators': [100, 200, 300],
            'learning_rate': [0.1, 0.2, 0.3],
        }
        xgb_model = XGBRegressor()
        grid_search = GridSearchCV(xgb_model, param_grid, cv=TimeSeriesSplit(n_splits=3), scoring='neg_mean_squared_error')
        grid_search.fit(data[feature_columns], data[[target_column]])
        xgb_model = grid_search.best_estimator_
        xgb_pred = xgb_model.predict(feature_pred)

        prophet_model = Prophet()
        for regressor in feature_columns:
            prophet_model.add_regressor(regressor)
        prophet_model.fit(data)
        # Predict future dates
        future = prediction_data[['ds']].copy()#prediction for 2 months
        for regressor in feature_columns:
            future[regressor] = feature_pred[regressor]  # Align feature values
        forecast = prophet_model.predict(future)
        prophet_pred = forecast['yhat'][-3:]
        return prophet_pred
        print (f'prophet output{prophet_pred}')



        predictions = []
        if es_accuracy > 50:
            predictions.append(es_pred)
        if sarima_accuracy > 50:
            predictions.append(sarima_pred)
        if xgb_accuracy > 50:
            predictions.append(xgb_pred)
        if prophet_accuracy > 50:
            predictions.append(prophet_pred)
        # Calculate ensemble prediction
        if predictions:
            ensemble_pred = ensemble_predict(np.array(predictions))
            return ensemble_pred
        if predictions and 'ensemble_pred' in locals():
          ensemble_filename = "ensemble_model.pkl"
          joblib.dump(ensemble_pred, ensemble_filename)
          print(f"Ensemble model saved as {ensemble_filename}")
        else:
            print("No models with accuracy greater than 50% to create an ensemble.")
