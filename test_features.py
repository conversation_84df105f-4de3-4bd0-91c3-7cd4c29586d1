#!/usr/bin/env python3
"""
Test script to verify that the forecast function still works with feature datasets
"""

import sys
import pandas as pd

# Add the current directory to the path
sys.path.append('.')

# Import the forecast functions
from sales_forecast.pipeline.forecast import predict_future, best_model, best_model_name, feature_columns

def test_with_features():
    """Test the function with feature datasets"""
    print("=== Testing Forecast with Features ===")
    
    if best_model is not None:
        print(f'Loaded model: {best_model_name}')
        print(f'Expected features: {feature_columns}')
        
        # Test with the correct features that the model was trained with
        test_data_with_features = pd.DataFrame({
            'ds': ['2024-07-01', '2024-08-01'],
            'MRP': [290, 295],
            'Discount value': [900000, 870000]
        })
        test_data_with_features['ds'] = pd.to_datetime(test_data_with_features['ds'])
        
        print('\nTest data (with features):')
        print(test_data_with_features)
        print()
        
        try:
            result = predict_future(best_model, best_model_name, test_data_with_features)
            print(f'Predictions: {result}')
            print(f'Prediction values: {[f"{val:.2f}" for val in result]}')
            
            # Check if predictions are meaningful (not all zeros)
            if all(val == 0 for val in result):
                print("❌ WARNING: All predictions are 0 - this suggests an issue!")
            else:
                print("✅ SUCCESS: Got meaningful predictions")
                
        except Exception as e:
            print(f'❌ ERROR: {e}')
            import traceback
            traceback.print_exc()
    else:
        print('No model loaded - cannot test')

def test_without_features():
    """Test the function without features"""
    print("\n=== Testing Forecast without Features ===")
    
    if best_model is not None:
        # Test with ds only (no features)
        test_data_ds_only = pd.DataFrame({
            'ds': ['2024-07-01', '2024-08-01']
        })
        test_data_ds_only['ds'] = pd.to_datetime(test_data_ds_only['ds'])
        
        print('Test data (ds only):')
        print(test_data_ds_only)
        print()
        
        try:
            result = predict_future(best_model, best_model_name, test_data_ds_only)
            print(f'Predictions: {result}')
            
            # For XGBoost, we expect zeros when no features
            if best_model_name == 'XGBoost':
                if all(val == 0 for val in result):
                    print("✅ SUCCESS: XGBoost correctly skipped (returned zeros)")
                else:
                    print("❌ WARNING: XGBoost should have returned zeros")
            else:
                print("✅ SUCCESS: Time-series model handled ds-only data")
                
        except Exception as e:
            print(f'❌ ERROR: {e}')
            import traceback
            traceback.print_exc()
    else:
        print('No model loaded - cannot test')

if __name__ == "__main__":
    test_with_features()
    test_without_features()
