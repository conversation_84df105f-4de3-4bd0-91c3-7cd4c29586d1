# import joblib

# feature_pred=input
# # Load the saved model from the joblib file
# model_path = "bestmodel.pkl"  # Replace with the actual path to your joblib file
# Best_model = joblib.load(model_path)
# # Replace with your feature data
# predictions = Best_model.predict(feature_pred)
# print(f"Predictions: {predictions}")
# # Example usage of the loaded model
# # Replace 'input_data' with the actual data you want to predict
# # input_data = [[value1, value2, value3, ...]]
# # prediction = loaded_model.predict(input_data)
# # print("Prediction:", prediction)

# 1. Import necessary libraries
import pandas as pd
import numpy as np
import joblib
import json
from xgboost import XGBRegressor
from statsmodels.tsa.statespace.sarimax import SARIMAX
from statsmodels.tsa.holtwinters import ExponentialSmoothing
from prophet import Prophet

# Initialize variables that will be set when the module is imported
date_column = None
feature_columns = None
target_column = None
best_model_name = None
best_model = None

# Try to load config if it exists
try:
    # 2. Load Config Files
    with open('models/config.json', 'r') as f:
        config = json.load(f)

    date_column = config['date_column']
    feature_columns = config['feature_columns']
    target_column = config['target_column']

    # Load Best Model Name
    with open('models/best_model_name.json', 'r') as f:
        model_info = json.load(f)
    best_model_name = model_info['best_model_name']

    # 3. Load Saved Best Model
    best_model = joblib.load('models/best_model.pkl')
    print(f"Loaded model: {best_model_name}")
except Exception as e:
    print(f"Note: No model loaded yet. {e}")

# Example prediction data format
# prediction_data = pd.DataFrame({
#     'ds': ['2024-07-01'],
#     'feature1': [value1],
#     'feature2': [value2]
# })
# prediction_data['ds'] = pd.to_datetime(prediction_data['ds'])

# 5. Forecasting Function
def predict_future(model, model_name, prediction_data, feature_cols=None):
    """
    Make predictions using the trained model

    Args:
        model: The trained model object
        model_name: String name of the model type
        prediction_data: DataFrame with feature values (must contain 'ds' column)
        feature_cols: List of feature column names (optional)

    Returns:
        Predictions as a numpy array
    """
    import numpy as np

    # Determine feature columns to use - prioritize what's actually available in the data
    if feature_cols is None:
        # Extract feature columns from prediction data (exclude 'ds' and 'y' columns)
        available_features = [col for col in prediction_data.columns if col not in ['ds', 'y']]

        if available_features:
            # Use features that are actually present in the data
            feature_cols = available_features
        elif feature_columns is not None:
            # Check if the expected features are actually in the data
            expected_features = [col for col in feature_columns if col in prediction_data.columns]
            feature_cols = expected_features if expected_features else []
        else:
            feature_cols = []

    # Handle case where feature_cols is empty or None
    has_features = feature_cols and len(feature_cols) > 0

    print(f"Predicting with {model_name}, Features available: {has_features}")
    if has_features:
        print(f"Using features: {feature_cols}")
    else:
        print("No feature columns available - using time-series only approach")

    if model_name == 'Exponential Smoothing':
        # Time-series model - doesn't need features
        pred = model.forecast(len(prediction_data))

    elif model_name == 'SARIMA':
        # Time-series model - doesn't need features
        pred = model.forecast(len(prediction_data))

    elif model_name == 'XGBoost':
        if has_features:
            # Use provided features
            pred = model.predict(prediction_data[feature_cols])
        else:
            # XGBoost requires features - skip prediction when none available
            print("Warning: XGBoost requires features but none are available. Skipping XGBoost prediction.")
            print("Consider using time-series models (Prophet, SARIMA, Exponential Smoothing) for datasets without features.")
            pred = np.array([0] * len(prediction_data))

    elif model_name == 'Prophet':
        try:
            # Prepare future dataframe for Prophet
            if 'ds' in prediction_data.columns:
                future = prediction_data[['ds']].copy()
                future['ds'] = pd.to_datetime(future['ds'])
            else:
                print("Error: Prophet requires 'ds' column")
                return np.array([0] * len(prediction_data))

            # Check if the model was trained with regressors
            model_regressors = getattr(model, 'extra_regressors', {})

            if model_regressors and not has_features:
                # Model was trained with features but none provided - skip Prophet
                print("Warning: Prophet model was trained with regressors but no features provided. Skipping Prophet prediction.")
                print(f"Expected regressors: {list(model_regressors.keys())}")
                print("Consider using time-series models without regressors or provide the required features.")
                pred = np.array([0] * len(prediction_data))
            else:
                # Add feature columns if available and expected
                if has_features:
                    for feature in feature_cols:
                        if feature in prediction_data.columns:
                            future[feature] = prediction_data[feature].values
                        else:
                            print(f"Warning: Feature '{feature}' not found in prediction data")
                            future[feature] = 0  # Default value
                elif model_regressors:
                    # Model expects regressors but none provided - add default values
                    for regressor in model_regressors.keys():
                        future[regressor] = 0  # Default value
                        print(f"Warning: Using default value (0) for missing regressor '{regressor}'")

                forecast = model.predict(future)
                pred = forecast['yhat'].values

        except Exception as e:
            print(f"Error in Prophet prediction: {e}")
            pred = np.array([0] * len(prediction_data))

    elif model_name == 'ensemble':
        pred = np.array([model] * len(prediction_data))

    else:
        raise ValueError(f"Unsupported model type: {model_name}")

    return pred




# Only run this code if the script is executed directly (not imported)
if __name__ == "__main__":
    print("=== Sales Forecasting Demo ===")

    # Example 1: Prediction data with features (traditional approach)
    print("\n1. Testing with feature columns:")
    prediction_data_with_features = pd.DataFrame({
        'ds': ['2024-07-01', '2024-08-01', '2024-09-01'],
        'MRP': [290, 295, 300],
        'Discount value': [900000, 870000, 890000]
    })
    prediction_data_with_features['ds'] = pd.to_datetime(prediction_data_with_features['ds'])

    # Example 2: Prediction data with only ds column (time-series only)
    print("\n2. Testing with only date column (ds):")
    prediction_data_ds_only = pd.DataFrame({
        'ds': ['2024-07-01', '2024-08-01', '2024-09-01']
    })
    prediction_data_ds_only['ds'] = pd.to_datetime(prediction_data_ds_only['ds'])

    # Run predictions if model is loaded
    if best_model is not None and best_model_name is not None:
        print(f"\nUsing loaded model: {best_model_name}")

        # Test with features
        print("\n📈 Forecast with Features:")
        try:
            forecast_with_features = predict_future(best_model, best_model_name, prediction_data_with_features)
            for date, pred in zip(prediction_data_with_features['ds'], forecast_with_features):
                print(f"{date.date()} → {pred:.2f}")
        except Exception as e:
            print(f"Error with features: {e}")

        # Test without features (ds only)
        print("\n📈 Forecast with DS only:")
        try:
            forecast_ds_only = predict_future(best_model, best_model_name, prediction_data_ds_only)
            for date, pred in zip(prediction_data_ds_only['ds'], forecast_ds_only):
                print(f"{date.date()} → {pred:.2f}")
        except Exception as e:
            print(f"Error with ds only: {e}")

    else:
        print("No model loaded. Please train a model first.")
        print("\n💡 Note: When no features are available:")
        print("   - Prophet, SARIMA, and Exponential Smoothing work with ds-only data")
        print("   - XGBoost will be skipped (requires features)")
        print("   - This makes the system work well with minimal datasets")
